@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';


/* Base body styles */
:global(body) {
  overflow-x: hidden;
}

/* Ensure background colors work properly */
:global(.bg-primary-400) {
  background-color: #f59e0b !important;
}

:global(.bg-primary-500) {
  background-color: #f59e0b !important;
}

:global(.bg-navy-800) {
  background-color: #1e293b !important;
}

:global(.bg-navy-900) {
  background-color: #0f172a !important;
}

:global(.bg-white) {
  background-color: #ffffff !important;
}

/* Gradient backgrounds */
:global(.gradient-bg) {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%) !important;
}


/* Card animations and effects */
:global(.service-card) {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

:global(.service-card:hover) {
  transform: translateY(-8px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

:global(.plan-card) {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

:global(.plan-card:hover) {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Mobile menu transitions */
:global(.mobile-menu) {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hamburger menu animations */
:global(.hamburger-line) {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

:global(.hamburger-active .line1) {
  transform: rotate(45deg) translate(5px, 5px);
}

:global(.hamburger-active .line2) {
  opacity: 0;
}

:global(.hamburger-active .line3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Scroll animations */
:global(.scroll-animation) {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

:global(.scroll-animation.animate) {
  opacity: 1;
  transform: translateY(0);
}


/* Ensure proper text colors for navigation */
:global(.text-white) {
  color: #ffffff !important;
}

:global(.text-gray-300) {
  color: #cbd5e1 !important;
}

:global(.text-primary-300) {
  color: #f8b96e !important;
}

:global(.text-primary-400) {
  color: #f59e0b !important;
}

:global(.text-navy-900) {
  color: #0f172a !important;
}

/* Font family overrides */
:global(.font-display) {
  font-family: 'Space Grotesk', sans-serif !important;
}

:global(.font-body) {
  font-family: 'Poppins', sans-serif !important;
}

/* Additional color fixes */
:global(.text-gray-200) {
  color: #e5e7eb !important;
}

:global(.text-gray-400) {
  color: #9ca3af !important;
}

:global(.text-gray-600) {
  color: #4b5563 !important;
}

:global(.text-primary-200) {
  color: #fbd6a6 !important;
}

:global(.text-primary-500) {
  color: #f59e0b !important;
}

:global(.text-primary-600) {
  color: #d97706 !important;
}

/* Ripple effect animation */
:global(.ripple) {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0);
  animation: ripple-animation 0.6s linear;
  pointer-events: none;
}

@keyframes ripple-animation {
  to {
    transform: scale(4);
    opacity: 0;
  }
}
